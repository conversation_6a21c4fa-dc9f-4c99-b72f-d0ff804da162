// @import './iconfont.css';

.test {
  // 可以通过 @apply 多个样式封装整体样式
  @apply mt-4 ml-4;

  padding-top: 4px;
  color: red;
}
.wd-tabs__nav-item.is-active {
  color: #248069 !important;
}

:root,
page {
  // 修改按主题色
  --wot-color-theme: #248069;
  --wot-tabs-nav-line-bg-color: #248069;
  --wot-button-success-bg-color: #248069;

  // 修改按钮背景色
  --wot-button-primary-bg-color: #248069;
  --wot-card-padding: 32rpx;
  //divider
  --wot-divider-color: #aaa;
  --wot-divider-margin: 0rpx;

  --wot-cell-vertical-top: 16rpx;
}
