# 登录功能演示指南

## 快速开始

### 1. 启动项目

```bash
# 安装依赖
npm install

# 启动 H5 开发服务器
npm run dev:h5

# 或启动微信小程序开发
npm run dev:mp-weixin
```

### 2. 访问测试页面

在浏览器中访问项目后，导航到测试页面：
- 路径：`/pages/test-login/index`
- 或直接访问：`http://localhost:3000/#/pages/test-login/index`

### 3. 测试登录功能

点击"跳转到登录页面"按钮，进入登录页面进行测试。

## 功能演示流程

### 第一步：选择登录方式
1. **微信授权登录**（绿色按钮）
   - 点击后模拟微信授权流程
   - 显示加载状态和成功提示
   - 直接跳转到身份选择页面

2. **手机号登录**（透明按钮）
   - 点击后显示登录表单
   - 支持密码登录和验证码登录切换

### 第二步：填写登录信息（手机号登录）

#### 密码登录模式
- 输入手机号（11位数字）
- 输入密码
- 可点击"忘记密码"或"立即注册"

#### 验证码登录模式
- 输入手机号（11位数字）
- 点击"获取验证码"按钮
- 输入6位验证码
- 验证码按钮有60秒倒计时

### 第三步：身份选择
登录成功后会显示身份选择页面：
- **意人（求职者）**：蓝色头像，找工作、投简历
- **老板（雇主）**：红色头像，招人才、发职位

选择身份后点击"确认"按钮完成登录流程。

### 第四步：隐私协议
- 页面底部固定显示隐私协议
- 必须勾选同意才能进行登录操作
- 可点击协议链接查看详情

## 测试数据

### 有效的手机号格式
- 13812345678
- 15987654321
- 18666888999

### 密码要求
- 任意密码都可以（演示环境）
- 实际项目中需要根据业务规则验证

### 验证码
- 任意6位数字（演示环境）
- 实际项目中需要接入短信服务

## 页面特性展示

### 视觉设计
- 渐变背景（蓝色到紫色）
- 毛玻璃效果
- 圆角设计
- 阴影效果
- 平滑动画过渡

### 交互体验
- 表单验证提示
- 加载状态显示
- 按钮点击反馈
- 倒计时功能
- 状态切换动画

### 响应式适配
- 支持不同屏幕尺寸
- 安全区域适配
- 触摸友好的按钮尺寸

## 开发调试

### 查看控制台
- 打开浏览器开发者工具
- 查看 Console 面板的日志输出
- 观察网络请求（模拟状态）

### 修改配置
- 编辑 `src/constant/login.ts` 修改文案和配置
- 编辑 `src/constant/images.ts` 修改图片资源
- 修改后会自动热重载

### 样式调试
- 使用 UnoCSS 原子化类名
- 在浏览器中实时修改类名查看效果
- 参考 `uno.config.ts` 了解可用的样式类

## 注意事项

1. **微信授权登录**：在实际微信小程序环境中才能正常工作
2. **短信验证码**：需要接入真实的短信服务提供商
3. **用户数据**：当前为演示数据，需要接入后端 API
4. **路由跳转**：根据实际项目调整跳转逻辑
5. **错误处理**：可以添加更详细的错误处理和重试机制

## 下一步开发

- [ ] 接入真实的登录 API
- [ ] 添加用户状态管理
- [ ] 完善错误处理机制
- [ ] 添加登录状态持久化
- [ ] 优化加载和错误状态展示
- [ ] 添加更多第三方登录方式
