<template>
  <view class="job-card" @click="handleCardClick">
    <!-- 紧急招聘标签 -->
    <view v-if="jobData.isUrgent" class="urgent-tag">
      <text class="urgent-text">急聘</text>
    </view>
    
    <!-- 职位标题和薪资 -->
    <view class="job-header">
      <view class="job-title">{{ jobData.title }}</view>
      <view class="job-salary">{{ jobData.salary }}</view>
    </view>
    
    <!-- 公司和地点 -->
    <view class="job-info">
      <view class="company-location">
        <text class="company-name">{{ jobData.company }}</text>
        <text class="location">{{ jobData.location }}</text>
      </view>
    </view>
    
    <!-- 标签列表 -->
    <view class="tags-container">
      <view 
        v-for="tag in jobData.tags" 
        :key="tag" 
        class="tag"
      >
        {{ tag }}
      </view>
    </view>
    
    <!-- 发布时间和收藏按钮 -->
    <view class="job-footer">
      <text class="publish-time">{{ jobData.publishTime }}</text>
      <view class="favorite-btn" @click.stop="handleFavorite">
        <uni-icons 
          :type="isFavorited ? 'heart-filled' : 'heart'" 
          :color="isFavorited ? '#ff4757' : '#ddd'"
          size="20"
        />
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import type { JobPosition } from '@/constant/recruitment'

interface Props {
  jobData: JobPosition
}

const props = defineProps<Props>()
const emit = defineEmits<{
  click: [job: JobPosition]
  favorite: [job: JobPosition, isFavorited: boolean]
}>()

const isFavorited = ref(false)

const handleCardClick = () => {
  emit('click', props.jobData)
}

const handleFavorite = () => {
  isFavorited.value = !isFavorited.value
  emit('favorite', props.jobData, isFavorited.value)
}
</script>

<style lang="scss" scoped>
.job-card {
  position: relative;
  background: #fff;
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 12px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.06);
  border: 1px solid #f0f0f0;
  
  &:active {
    background: #f8f9fa;
  }
}

.urgent-tag {
  position: absolute;
  top: 0;
  right: 12px;
  background: linear-gradient(135deg, #ff6b6b, #ff5252);
  border-radius: 0 0 8px 8px;
  padding: 4px 8px;
  
  .urgent-text {
    color: #fff;
    font-size: 12px;
    font-weight: 500;
  }
}

.job-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 8px;
  
  .job-title {
    font-size: 16px;
    font-weight: 600;
    color: #333;
    flex: 1;
    margin-right: 12px;
  }
  
  .job-salary {
    font-size: 16px;
    font-weight: 600;
    color: #248069;
  }
}

.job-info {
  margin-bottom: 12px;
  
  .company-location {
    display: flex;
    align-items: center;
    gap: 12px;
    
    .company-name {
      font-size: 14px;
      color: #666;
    }
    
    .location {
      font-size: 14px;
      color: #999;
      
      &::before {
        content: '📍';
        margin-right: 4px;
      }
    }
  }
}

.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
  margin-bottom: 12px;
  
  .tag {
    background: #f0f9ff;
    color: #0369a1;
    font-size: 12px;
    padding: 4px 8px;
    border-radius: 4px;
    border: 1px solid #e0f2fe;
  }
}

.job-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  
  .publish-time {
    font-size: 12px;
    color: #999;
  }
  
  .favorite-btn {
    padding: 4px;
    
    &:active {
      opacity: 0.7;
    }
  }
}
</style>
