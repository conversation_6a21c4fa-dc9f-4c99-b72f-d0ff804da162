<!-- 使用 type="home" 属性设置首页，其他页面不需要设置，默认为page；推荐使用json5，更强大，且允许注释 -->
<route lang="json5" type="home">
{
  layout: 'common',
  style: {
    navigationStyle: 'custom',
    navigationBarTitleText: '意仁直聘',
    leftTextClass: 'font-bold text-primary',
  },
  isTab: true,
}
</route>
<template>
  <view class="recruitment-page">
    <!-- 搜索栏 -->
    <view class="search-container">
      <view class="search-box">
        <uni-icons type="search" color="#999" size="16" />
        <input
          class="search-input"
          placeholder="搜索求职，专业"
          v-model="searchKeyword"
          @confirm="handleSearch"
        />
        <view class="search-btn" @click="handleSearch">
          <text class="search-btn-text">搜索</text>
        </view>
      </view>
    </view>

    <!-- 招聘横幅 -->
    <view class="banner-container">
      <view class="banner-background"></view>
      <view class="banner-overlay">
        <text class="banner-title">JOIN US</text>
        <text class="banner-subtitle">加入我们</text>
      </view>
    </view>

    <!-- 推荐招聘职位标题 -->
    <view class="section-header">
      <text class="section-title">推荐招聘职位</text>
    </view>

    <!-- 筛选标签 -->
    <view class="filter-container">
      <scroll-view class="filter-scroll" scroll-x>
        <view class="filter-tags">
          <view
            v-for="tag in filterTags"
            :key="tag.id"
            class="filter-tag"
            :class="{ active: activeFilterTag === tag.id }"
            @click="handleFilterChange(tag.id)"
          >
            <uni-icons v-if="tag.id === 'all'" type="list" color="currentColor" size="14" />
            <uni-icons
              v-else-if="tag.id === 'intern'"
              type="staff"
              color="currentColor"
              size="14"
            />
            <uni-icons
              v-else-if="tag.id === 'location'"
              type="location"
              color="currentColor"
              size="14"
            />
            <uni-icons v-else type="settings" color="currentColor" size="14" />
            <text class="filter-tag-text">{{ tag.label }}</text>
          </view>
          <view class="filter-more">
            <uni-icons type="more-filled" color="#666" size="16" />
            <text class="filter-more-text">筛选</text>
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 职位列表 -->
    <view class="job-list-container">
      <job-card
        v-for="job in filteredJobs"
        :key="job.id"
        :job-data="job"
        @click="handleJobClick"
        @favorite="handleJobFavorite"
      />
    </view>

    <!-- 底部导航栏 -->
    <view class="bottom-tabbar">
      <view
        v-for="tab in tabBarList"
        :key="tab.id"
        class="tab-item"
        :class="{ active: activeTab === tab.id }"
        @click="handleTabChange(tab.id)"
      >
        <view v-if="tab.id === 'add'" class="add-btn">
          <uni-icons type="plus" color="#fff" size="24" />
        </view>
        <template v-else>
          <uni-icons
            :type="getTabIcon(tab.id)"
            :color="activeTab === tab.id ? '#248069' : '#999'"
            size="20"
          />
          <text class="tab-text" :class="{ active: activeTab === tab.id }">
            {{ tab.text }}
          </text>
        </template>
      </view>
    </view>
  </view>
</template>

<script lang="ts" setup>
import JobCard from '@/components/job-card/job-card.vue'
import {
  JOB_POSITIONS,
  FILTER_TAGS,
  TAB_BAR_LIST,
  type JobPosition,
  type FilterTag,
} from '@/constant/recruitment'

defineOptions({
  name: 'RecruitmentHome',
})

// 获取屏幕边界到安全区域距离
const { safeAreaInsets } = uni.getSystemInfoSync()

// 响应式数据
const searchKeyword = ref('')
const activeFilterTag = ref('all')
const activeTab = ref('recruit')
const jobList = ref<JobPosition[]>(JOB_POSITIONS)
const filterTags = ref<FilterTag[]>(FILTER_TAGS)
const tabBarList = ref(TAB_BAR_LIST)

// 计算属性
const filteredJobs = computed(() => {
  if (activeFilterTag.value === 'all') {
    return jobList.value
  }
  // 这里可以根据不同的筛选条件进行过滤
  return jobList.value
})

// 方法
const handleSearch = () => {
  if (!searchKeyword.value.trim()) {
    uni.showToast({
      title: '请输入搜索关键词',
      icon: 'none',
    })
    return
  }

  // 执行搜索逻辑
  console.log('搜索关键词:', searchKeyword.value)
  uni.showToast({
    title: `搜索: ${searchKeyword.value}`,
    icon: 'none',
  })
}

const handleFilterChange = (tagId: string) => {
  activeFilterTag.value = tagId
  console.log('切换筛选标签:', tagId)
}

const handleJobClick = (job: JobPosition) => {
  console.log('点击职位:', job.title)
  uni.showToast({
    title: `查看职位: ${job.title}`,
    icon: 'none',
  })
}

const handleJobFavorite = (job: JobPosition, isFavorited: boolean) => {
  const action = isFavorited ? '收藏' : '取消收藏'
  console.log(`${action}职位:`, job.title)
  uni.showToast({
    title: `${action}: ${job.title}`,
    icon: 'none',
  })
}

const handleTabChange = (tabId: string) => {
  if (tabId === activeTab.value) return

  activeTab.value = tabId
  console.log('切换标签:', tabId)

  // 根据不同标签进行页面跳转
  switch (tabId) {
    case 'message':
      uni.showToast({ title: '消息功能开发中', icon: 'none' })
      break
    case 'add':
      uni.showToast({ title: '发布功能开发中', icon: 'none' })
      break
    case 'forum':
      uni.showToast({ title: '论坛功能开发中', icon: 'none' })
      break
    case 'mine':
      uni.showToast({ title: '我的功能开发中', icon: 'none' })
      break
  }
}

const getTabIcon = (tabId: string) => {
  const iconMap: Record<string, string> = {
    recruit: 'staff',
    message: 'chat',
    forum: 'chatbubble',
    mine: 'person',
  }
  return iconMap[tabId] || 'home'
}

// 生命周期
onLoad(() => {
  console.log('招聘页面加载完成')
})
</script>

<style lang="scss" scoped>
.recruitment-page {
  min-height: 100vh;
  padding-bottom: 60px; // 为底部导航栏留出空间
}

// 自定义导航栏
.custom-navbar {
  border-bottom: 1px solid #f0f0f0;

  .navbar-content {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 12px 16px;

    .app-title {
      font-size: 18px;
      font-weight: 600;
      color: #248069;
    }

    .navbar-actions {
      display: flex;
      align-items: center;
      gap: 16px;

      .record-btn {
        padding: 4px;
      }
    }
  }
}

// 搜索栏
.search-container {
  .search-box {
    display: flex;
    align-items: center;
    background: #f8f9fa;
    border-radius: 20px;
    padding: 8px 12px;
    gap: 8px;

    .search-input {
      flex: 1;
      font-size: 14px;
      color: #333;

      &::placeholder {
        color: #999;
      }
    }

    .search-btn {
      background: #248069;
      border-radius: 16px;
      padding: 6px 16px;

      .search-btn-text {
        color: #fff;
        font-size: 14px;
      }
    }
  }
}

// 横幅
.banner-container {
  position: relative;
  margin: 12px 16px;
  border-radius: 12px;
  overflow: hidden;
  height: 160px;

  .banner-background {
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  }

  .banner-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    background: rgba(0, 0, 0, 0.3);

    .banner-title {
      font-size: 32px;
      font-weight: bold;
      color: #fff;
      margin-bottom: 8px;
      letter-spacing: 2px;
    }

    .banner-subtitle {
      font-size: 16px;
      color: #fff;
      opacity: 0.9;
    }
  }
}

// 章节标题
.section-header {
  padding: 16px;

  .section-title {
    font-size: 18px;
    font-weight: 600;
    color: #333;
  }
}

// 筛选容器
.filter-container {
  background: #fff;
  padding: 12px 0;
  margin-bottom: 8px;

  .filter-scroll {
    white-space: nowrap;
  }

  .filter-tags {
    display: flex;
    align-items: center;
    padding: 0 16px;
    gap: 12px;

    .filter-tag {
      display: flex;
      align-items: center;
      gap: 4px;
      padding: 6px 12px;
      border-radius: 16px;
      background: #f8f9fa;
      border: 1px solid #e9ecef;
      white-space: nowrap;
      transition: all 0.2s;

      &.active {
        background: #248069;
        border-color: #248069;
        color: #fff;
      }

      .filter-tag-text {
        font-size: 14px;
        color: inherit;
      }
    }

    .filter-more {
      display: flex;
      align-items: center;
      gap: 4px;
      padding: 6px 12px;
      border-radius: 16px;
      background: #f8f9fa;
      border: 1px solid #e9ecef;
      white-space: nowrap;

      .filter-more-text {
        font-size: 14px;
        color: #666;
      }
    }
  }
}

// 职位列表
.job-list-container {
  padding: 0 16px;
}

// 底部导航栏
.bottom-tabbar {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background: #fff;
  border-top: 1px solid #f0f0f0;
  display: flex;
  align-items: center;
  height: 60px;
  z-index: 1000;

  .tab-item {
    flex: 1;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    gap: 4px;
    padding: 8px 4px;

    &.active .tab-text {
      color: #248069;
    }

    .add-btn {
      width: 40px;
      height: 40px;
      background: linear-gradient(135deg, #248069, #1a5f56);
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      box-shadow: 0 2px 8px rgba(36, 128, 105, 0.3);
    }

    .tab-text {
      font-size: 12px;
      color: #999;
      transition: color 0.2s;

      &.active {
        color: #248069;
      }
    }
  }
}
</style>
