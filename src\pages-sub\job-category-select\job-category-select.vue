<route lang="json5" type="page">
{
  style: {
    navigationBarTitleText: '职业类别',
    navigationStyle: 'default',
  },
}
</route>

<template>
  <view class="min-h-screen bg-gray-50">
    <!-- 搜索框 -->
    <view class="bg-white p-4 border-b border-gray-100">
      <view class="flex gap-3">
        <wd-input v-model="searchKeyword" placeholder="搜索职业" class="flex-1" @input="onSearch" />
        <button
          class="bg-primary text-white px-4 py-2 rounded-lg text-sm font-medium active:bg-primary-600 transition-colors"
          @click="searchCategory"
        >
          搜索
        </button>
      </view>
    </view>

    <!-- 职业类别列表 -->
    <view class="bg-white mb-6">
      <!-- 台前 -->
      <view class="p-4">
        <view class="text-base font-medium text-gray-900 mb-3">台前</view>
        <view class="grid grid-cols-3 gap-3 mb-6">
          <button
            v-for="category in frontCategories"
            :key="category.value"
            :class="[
              'py-2 px-3 rounded-lg text-sm border transition-colors',
              selectedCategories.includes(category.value)
                ? 'bg-primary-50 border-primary text-primary'
                : 'bg-white border-gray-200 text-gray-700',
            ]"
            @click="toggleCategory(category.value)"
          >
            {{ category.label }}
          </button>
        </view>

        <!-- 幕后 -->
        <view class="text-base font-medium text-gray-900 mb-3">幕后</view>
        <view class="grid grid-cols-3 gap-3 mb-6">
          <button
            v-for="category in behindCategories"
            :key="category.value"
            :class="[
              'py-2 px-3 rounded-lg text-sm border transition-colors',
              selectedCategories.includes(category.value)
                ? 'bg-primary-50 border-primary text-primary'
                : 'bg-white border-gray-200 text-gray-700',
            ]"
            @click="toggleCategory(category.value)"
          >
            {{ category.label }}
          </button>
        </view>

        <!-- 运营 -->
        <view class="text-base font-medium text-gray-900 mb-3">运营</view>
        <view class="grid grid-cols-3 gap-3 mb-6">
          <button
            v-for="category in operationCategories"
            :key="category.value"
            :class="[
              'py-2 px-3 rounded-lg text-sm border transition-colors',
              selectedCategories.includes(category.value)
                ? 'bg-primary-50 border-primary text-primary'
                : 'bg-white border-gray-200 text-gray-700',
            ]"
            @click="toggleCategory(category.value)"
          >
            {{ category.label }}
          </button>
        </view>

        <!-- 主持/互动 -->
        <view class="text-base font-medium text-gray-900 mb-3">主持/互动</view>
        <view class="grid grid-cols-3 gap-3 mb-6">
          <button
            v-for="category in hostCategories"
            :key="category.value"
            :class="[
              'py-2 px-3 rounded-lg text-sm border transition-colors',
              selectedCategories.includes(category.value)
                ? 'bg-primary-50 border-primary text-primary'
                : 'bg-white border-gray-200 text-gray-700',
            ]"
            @click="toggleCategory(category.value)"
          >
            {{ category.label }}
          </button>
        </view>
      </view>
    </view>

    <!-- 已选择的职业类别 -->
    <view v-if="selectedCategories.length > 0" class="bg-white mb-6">
      <view class="px-4 py-3 border-b border-gray-100">
        <text class="text-base font-medium text-gray-900">已选择</text>
      </view>
      <view class="p-4">
        <view class="flex flex-wrap gap-2">
          <wd-tag
            v-for="category in selectedCategories"
            :key="category"
            type="success"
            size="small"
            closable
            @close="removeCategory(category)"
          >
            {{ getCategoryLabel(category) }}
          </wd-tag>
        </view>
      </view>
    </view>

    <!-- 底部按钮 -->
    <view class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 p-4">
      <view class="flex gap-3">
        <button
          class="flex-1 bg-gray-100 text-gray-700 py-3 rounded-lg text-base font-medium active:bg-gray-200 transition-colors"
          @click="resetSelection"
        >
          重置
        </button>
        <button
          class="flex-1 bg-primary text-white py-3 rounded-lg text-base font-medium active:bg-primary-600 transition-colors"
          @click="confirmSelection"
        >
          确定
        </button>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { toast } from '@/utils/toast'
import {
  getJobCategoryList,
  searchJobCategories,
  type JobCategoryItem,
} from '@/service/index/filter'

// 职业类别数据类型
interface CategoryItem {
  label: string
  value: string
  group: string
}

// 响应式数据
const searchKeyword = ref('')
const selectedCategories = ref<string[]>(['舞蹈类', '驻场主持人'])

// 台前类别
const frontCategories = ref<CategoryItem[]>([{ label: '演员', value: '演员', group: '台前' }])

// 幕后类别
const behindCategories = ref<CategoryItem[]>([
  { label: '舞蹈类', value: '舞蹈类', group: '幕后' },
  { label: '表演类', value: '表演类', group: '幕后' },
  { label: '武术类', value: '武术类', group: '幕后' },
  { label: '杂技类', value: '杂技类', group: '幕后' },
  { label: '音乐类', value: '音乐类', group: '幕后' },
  { label: '模特类', value: '模特类', group: '幕后' },
])

// 运营类别
const operationCategories = ref<CategoryItem[]>([])

// 主持/互动类别
const hostCategories = ref<CategoryItem[]>([
  { label: '驻场主持人', value: '驻场主持人', group: '主持/互动' },
  { label: '角色扮演互动员', value: '角色扮演互动员', group: '主持/互动' },
  { label: '古装NPC', value: '古装NPC', group: '主持/互动' },
])

// 所有类别
const allCategories = computed(() => [
  ...frontCategories.value,
  ...behindCategories.value,
  ...operationCategories.value,
  ...hostCategories.value,
])

// 过滤后的类别（用于搜索）
const filteredCategories = computed(() => {
  if (!searchKeyword.value) {
    return allCategories.value
  }
  return allCategories.value.filter((category) => category.label.includes(searchKeyword.value))
})

// 获取类别标签
const getCategoryLabel = (value: string) => {
  const category = allCategories.value.find((cat) => cat.value === value)
  return category ? category.label : value
}

// 搜索职业类别
const searchCategory = () => {
  if (!searchKeyword.value.trim()) {
    toast.info('请输入搜索关键词')
    return
  }
  // 搜索逻辑已在computed中实现
}

// 输入搜索
const onSearch = () => {
  // 实时搜索，computed会自动更新
}

// 切换类别选择
const toggleCategory = (category: string) => {
  const index = selectedCategories.value.indexOf(category)
  if (index > -1) {
    selectedCategories.value.splice(index, 1)
  } else {
    selectedCategories.value.push(category)
  }
}

// 移除类别
const removeCategory = (category: string) => {
  const index = selectedCategories.value.indexOf(category)
  if (index > -1) {
    selectedCategories.value.splice(index, 1)
  }
}

// 重置选择
const resetSelection = () => {
  selectedCategories.value = []
  searchKeyword.value = ''
  toast.success('已重置选择')
}

// 确定选择
const confirmSelection = () => {
  const selectedLabels = selectedCategories.value.map((category) => getCategoryLabel(category))

  console.log('选择的职业类别:', selectedLabels)
  toast.success(`已选择 ${selectedCategories.value.length} 个职业类别`)

  // 返回上一页并传递选择的类别数据
  uni.navigateBack()
}
</script>
