/* eslint-disable */
// @ts-ignore
import { queryOptions, useMutation } from '@tanstack/vue-query';
import type { DefaultError } from '@tanstack/vue-query';
import request from '@/utils/request';
import { CustomRequestOptions } from '@/interceptors/request';

import * as apis from './guanlihoutaishangpinliulanjilu';
import * as API from './types';

/** 获得商品浏览记录分页 GET /admin-api/product/browse-history/page */
export function getBrowseHistoryPage1QueryOptions(options: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.getBrowseHistoryPage1Params;
  options?: CustomRequestOptions;
}) {
  return queryOptions({
    queryFn: async ({ queryKey }) => {
      return apis.getBrowseHistoryPage1(queryKey[1] as typeof options);
    },
    queryKey: ['getBrowseHistoryPage1', options],
  });
}
