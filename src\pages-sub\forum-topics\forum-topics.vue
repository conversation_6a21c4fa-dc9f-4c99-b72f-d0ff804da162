<route lang="json5">
{
  style: {
    navigationBarTitleText: '热门话题',
    navigationStyle: 'custom',
  },
}
</route>

<template>
  <view class="min-h-screen bg-gray-50">
    <!-- 自定义导航栏 -->
    <wd-navbar
      :bordered="false"
      left-arrow
      title="热门话题"
      custom-class="font-bold"
      fixed
      safeAreaInsetTop
      @click-left="handleBack"
    >
      <template #right>
        <view class="flex items-center gap-3">
          <wd-icon name="more-horizontal" size="20px" @click="handleMore" />
          <wd-icon name="help" size="20px" @click="handleHelp" />
        </view>
      </template>
    </wd-navbar>

    <scroll-view
      :scroll-y="true"
      class="h-100vh"
      :style="{
        paddingTop: safeAreaInsets?.top + 44 + 'px',
      }"
      @scrolltolower="loadMore"
    >
      <!-- 话题列表 -->
      <view class="mx-3 mt-3 space-y-3">
        <view
          v-for="topic in topicList"
          :key="topic.id"
          class="bg-white rounded-3 p-4"
          @click="goToTopicDetail(topic.id)"
        >
          <view class="flex items-center justify-between">
            <!-- 话题信息 -->
            <view class="flex items-center flex-1">
              <view class="flex items-center justify-center w-10 h-10 bg-primary rounded-full mr-3">
                <wd-icon name="add" size="16px" color="white" />
              </view>
              <view class="flex-1">
                <view class="flex items-center mb-1">
                  <text class="text-4 font-medium text-gray-800">{{ topic.name }}</text>
                  <wd-icon 
                    v-if="topic.isHot" 
                    name="fire" 
                    size="14px" 
                    color="#ff4757" 
                    class="ml-2" 
                  />
                </view>
                <text class="text-3 text-gray-500">{{ topic.participantCount }}人参与</text>
              </view>
            </view>

            <!-- 参与按钮 -->
            <wd-button
              type="success"
              size="small"
              plain
              custom-class="px-4"
              @click.stop="participateTopic(topic.id)"
            >
              {{ topic.isParticipated ? '已参与' : '去参与' }}
            </wd-button>
          </view>

          <!-- 话题描述 -->
          <view v-if="topic.description" class="mt-3 pl-13">
            <text class="text-3 text-gray-600 leading-relaxed">{{ topic.description }}</text>
          </view>

          <!-- 最新帖子预览 -->
          <view v-if="topic.latestPosts && topic.latestPosts.length > 0" class="mt-3 pl-13">
            <text class="text-2 text-gray-400 mb-2 block">最新讨论</text>
            <view class="space-y-2">
              <view
                v-for="post in topic.latestPosts.slice(0, 2)"
                :key="post.id"
                class="bg-gray-50 rounded-2 p-2"
                @click.stop="goToPostDetail(post.id)"
              >
                <text class="text-2 text-gray-600 line-clamp-2">{{ post.content }}</text>
                <view class="flex items-center justify-between mt-1">
                  <text class="text-1 text-gray-400">{{ post.author.name }}</text>
                  <text class="text-1 text-gray-400">{{ post.publishTime }}</text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 加载更多 -->
      <view v-if="loading" class="flex justify-center py-4">
        <wd-loading type="spinner" />
      </view>

      <!-- 没有更多数据 -->
      <view v-if="!hasMore && topicList.length > 0" class="text-center py-4">
        <text class="text-3 text-gray-400">没有更多话题了</text>
      </view>

      <!-- 空状态 -->
      <view v-if="!loading && topicList.length === 0" class="flex flex-col items-center justify-center py-20">
        <wd-icon name="chat" size="48px" color="#ccc" />
        <text class="text-3 text-gray-400 mt-4">暂无热门话题</text>
        <wd-button type="primary" size="small" class="mt-4" @click="refreshTopics">
          刷新试试
        </wd-button>
      </view>
    </scroll-view>
  </view>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue'
import { toast } from '@/utils/toast'
import { getSystemInfoSync } from '@/utils'
import {
  getHotTopics,
  toggleTopicFollow,
  type ForumTopic
} from '@/service/index/forum'

const { safeAreaInsets } = getSystemInfoSync()

// 页面状态
const loading = ref(false)
const hasMore = ref(true)
const currentPage = ref(1)
const pageSize = ref(20)

// 扩展话题类型
interface ExtendedForumTopic extends ForumTopic {
  participantCount: number
  isParticipated: boolean
  latestPosts?: {
    id: string
    content: string
    author: {
      name: string
    }
    publishTime: string
  }[]
}

// 话题列表数据
const topicList = ref<ExtendedForumTopic[]>([
  {
    id: '1',
    name: '话题名称',
    description: '这是一个关于舞蹈技巧分享的话题，大家可以在这里交流各种舞蹈心得和技巧。',
    count: 1233,
    participantCount: 1233,
    isHot: true,
    isFollowed: false,
    isParticipated: false,
    latestPosts: [
      {
        id: '1',
        content: '刚刚学会了一个新的舞蹈动作，想和大家分享一下心得...',
        author: { name: '舞蹈小王子' },
        publishTime: '2小时前'
      },
      {
        id: '2',
        content: '有没有人知道这个动作怎么做得更标准？',
        author: { name: '舞蹈爱好者' },
        publishTime: '3小时前'
      }
    ]
  },
  {
    id: '2',
    name: '话题名称',
    description: '招聘信息分享，包括各种演出机会、工作岗位等。',
    count: 1233,
    participantCount: 1233,
    isHot: true,
    isFollowed: false,
    isParticipated: true,
    latestPosts: []
  },
  {
    id: '3',
    name: '话题名称',
    description: '武术交流讨论，分享武术心得和技巧。',
    count: 1233,
    participantCount: 1233,
    isHot: true,
    isFollowed: false,
    isParticipated: false,
    latestPosts: []
  },
  {
    id: '4',
    name: '话题名称',
    description: '一般性话题讨论，各种日常交流。',
    count: 1233,
    participantCount: 1233,
    isHot: false,
    isFollowed: false,
    isParticipated: false,
    latestPosts: []
  },
  {
    id: '5',
    name: '话题名称',
    description: '更多有趣的话题等你来发现和参与。',
    count: 1233,
    participantCount: 1233,
    isHot: false,
    isFollowed: false,
    isParticipated: false,
    latestPosts: []
  }
])

onMounted(() => {
  loadTopicList()
})

// 加载话题列表
const loadTopicList = async (isRefresh = false) => {
  try {
    loading.value = true
    
    if (isRefresh) {
      currentPage.value = 1
      hasMore.value = true
    }
    
    const res = await getHotTopics({
      params: {
        limit: pageSize.value
      }
    })
    
    if (res.code === 0) {
      // 转换数据格式
      const topics = res.data.map(topic => ({
        ...topic,
        participantCount: topic.count,
        isParticipated: Math.random() > 0.5, // 模拟参与状态
        latestPosts: Math.random() > 0.5 ? [
          {
            id: '1',
            content: '这是一个示例帖子内容，展示话题下的最新讨论...',
            author: { name: '用户' + Math.floor(Math.random() * 100) },
            publishTime: Math.floor(Math.random() * 24) + '小时前'
          }
        ] : []
      }))
      
      if (isRefresh) {
        topicList.value = topics
      } else {
        topicList.value.push(...topics)
      }
      
      hasMore.value = topics.length === pageSize.value
      currentPage.value++
    } else {
      toast.error(res.msg || '加载失败')
    }
  } catch (error) {
    console.error('加载话题列表失败:', error)
    toast.error('加载失败')
  } finally {
    loading.value = false
  }
}

// 加载更多
const loadMore = async () => {
  if (loading.value || !hasMore.value) return
  await loadTopicList(false)
}

// 刷新话题
const refreshTopics = () => {
  loadTopicList(true)
}

// 返回上一页
const handleBack = () => {
  uni.navigateBack()
}

// 更多操作
const handleMore = () => {
  uni.showActionSheet({
    itemList: ['搜索话题', '创建话题', '我的话题'],
    success: (res) => {
      switch (res.tapIndex) {
        case 0:
          toast.info('搜索话题功能')
          break
        case 1:
          goToCreateTopic()
          break
        case 2:
          goToMyTopics()
          break
      }
    }
  })
}

// 帮助
const handleHelp = () => {
  toast.info('话题帮助功能')
}

// 参与话题
const participateTopic = async (topicId: string) => {
  const topic = topicList.value.find(t => t.id === topicId)
  if (!topic) return

  try {
    const action = topic.isParticipated ? 'unfollow' : 'follow'
    const res = await toggleTopicFollow({
      body: { topicId, action }
    })
    
    if (res.code === 0) {
      topic.isParticipated = res.data.isFollowed
      topic.participantCount += topic.isParticipated ? 1 : -1
      toast.success(topic.isParticipated ? '参与成功' : '取消参与')
    } else {
      toast.error(res.msg || '操作失败')
    }
  } catch (error) {
    console.error('参与话题失败:', error)
    toast.error('操作失败')
  }
}

// 跳转到话题详情
const goToTopicDetail = (topicId: string) => {
  uni.navigateTo({
    url: `/pages-sub/forum-topic-detail/forum-topic-detail?id=${topicId}`
  })
}

// 跳转到帖子详情
const goToPostDetail = (postId: string) => {
  uni.navigateTo({
    url: `/pages-sub/forum-post-detail/forum-post-detail?id=${postId}`
  })
}

// 跳转到创建话题
const goToCreateTopic = () => {
  uni.navigateTo({
    url: '/pages-sub/forum-create-topic/forum-create-topic'
  })
}

// 跳转到我的话题
const goToMyTopics = () => {
  uni.navigateTo({
    url: '/pages-sub/forum-my-topics/forum-my-topics'
  })
}
</script>
