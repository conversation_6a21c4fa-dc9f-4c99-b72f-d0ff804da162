/* eslint-disable */
// @ts-ignore
import { queryOptions, useMutation } from '@tanstack/vue-query';
import type { DefaultError } from '@tanstack/vue-query';
import request from '@/utils/request';
import { CustomRequestOptions } from '@/interceptors/request';

import * as apis from './guanlihoutaihui<PERSON>den<PERSON>ji';
import * as API from './types';

/** 创建会员等级 POST /admin-api/member/level/create */
export function useCreateLevelMutation(options?: {
  onSuccess?: (value?: API.CommonResultLong) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.createLevel,
    onSuccess(data: API.CommonResultLong) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 删除会员等级 DELETE /admin-api/member/level/delete */
export function useDeleteLevelMutation(options?: {
  onSuccess?: (value?: API.CommonResultBoolean) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.deleteLevel,
    onSuccess(data: API.CommonResultBoolean) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}

/** 获得会员等级 GET /admin-api/member/level/get */
export function getLevelQueryOptions(options: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.getLevelParams;
  options?: CustomRequestOptions;
}) {
  return queryOptions({
    queryFn: async ({ queryKey }) => {
      return apis.getLevel(queryKey[1] as typeof options);
    },
    queryKey: ['getLevel', options],
  });
}

/** 获得会员等级列表 GET /admin-api/member/level/list */
export function getLevelList1QueryOptions(options: {
  // 叠加生成的Param类型 (非body参数openapi默认没有生成对象)
  params: API.getLevelList1Params;
  options?: CustomRequestOptions;
}) {
  return queryOptions({
    queryFn: async ({ queryKey }) => {
      return apis.getLevelList1(queryKey[1] as typeof options);
    },
    queryKey: ['getLevelList1', options],
  });
}

/** 获取会员等级精简信息列表 只包含被开启的会员等级，主要用于前端的下拉选项 GET /admin-api/member/level/list-all-simple */
export function getSimpleLevelListQueryOptions(options: {
  options?: CustomRequestOptions;
}) {
  return queryOptions({
    queryFn: async ({ queryKey }) => {
      return apis.getSimpleLevelList(queryKey[1] as typeof options);
    },
    queryKey: ['getSimpleLevelList', options],
  });
}

/** 更新会员等级 PUT /admin-api/member/level/update */
export function useUpdateLevelMutation(options?: {
  onSuccess?: (value?: API.CommonResultBoolean) => void;
  onError?: (error?: DefaultError) => void;
}) {
  const { onSuccess, onError } = options || {};

  const response = useMutation({
    mutationFn: apis.updateLevel,
    onSuccess(data: API.CommonResultBoolean) {
      onSuccess?.(data);
    },
    onError(error) {
      onError?.(error);
    },
  });

  return response;
}
