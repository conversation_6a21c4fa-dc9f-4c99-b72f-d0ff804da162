/* eslint-disable */
// @ts-ignore
import * as API from './types';

export function displayRepCodeEnumEnum(field: API.IRepCodeEnumEnum) {
  return {
    SUCCESS: 'SUCCESS',
    ERROR: 'ERROR',
    EXCEPTION: 'EXCEPTION',
    BLANK_ERROR: 'BLANK_ERROR',
    NULL_ERROR: 'NULL_ERROR',
    NOT_NULL_ERROR: 'NOT_NULL_ERROR',
    NOT_EXIST_ERROR: 'NOT_EXIST_ERROR',
    EXIST_ERROR: 'EXIST_ERROR',
    PARAM_TYPE_ERROR: 'PARAM_TYPE_ERROR',
    PARAM_FORMAT_ERROR: 'PARAM_FORMAT_ERROR',
    API_CAPTCHA_INVALID: 'API_CAPTCHA_INVALID',
    API_CAPTCHA_COORDINATE_ERROR: 'API_CAPTCHA_COORDINATE_ERROR',
    API_CAPTCHA_ERROR: 'API_CAPTCHA_ERROR',
    API_CAPTCHA_BASEMAP_NULL: 'API_CAPTCHA_BASEMAP_NULL',
    API_REQ_LIMIT_GET_ERROR: 'API_REQ_LIMIT_GET_ERROR',
    API_REQ_INVALID: 'API_REQ_INVALID',
    API_REQ_LOCK_GET_ERROR: 'API_REQ_LOCK_GET_ERROR',
    API_REQ_LIMIT_CHECK_ERROR: 'API_REQ_LIMIT_CHECK_ERROR',
    API_REQ_LIMIT_VERIFY_ERROR: 'API_REQ_LIMIT_VERIFY_ERROR',
  }[field];
}
