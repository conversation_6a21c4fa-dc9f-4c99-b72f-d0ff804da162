<route lang="json5" type="page">
{
  layout: 'common',
  style: {
    navigationBarTitleText: '地区选择',
    navigationStyle: 'custom',
  },
}
</route>

<template>
  <view class="pt-2">
    <search class-name="mx-4" @confirm="onConfirm"></search>
    <view class="flex mt-2 gap-1" style="height: calc(100vh - 460rpx)">
      <wd-sidebar v-model="province">
        <wd-sidebar-item
          v-for="item in locations"
          :key="item.value"
          :value="item.value"
          :label="item.label"
          custom-class="!py-2"
        />
      </wd-sidebar>
      <scroll-view
        class="flex-1"
        style="border-right: 2rpx solid #eee"
        scroll-y
        scroll-with-animation
        :throttle="false"
      >
        <view class="flex justify-between items-center flex-wrap">
          <view
            v-for="ite in citiesList"
            :key="ite.value"
            :class="`flex  items-center w-full h-[32px] !py-2 px-3 ${cities === ite.value ? ' !text-primary' : ''}`"
            @click="cities = ite.value"
          >
            {{ ite.label }}
          </view>
        </view>
      </scroll-view>
      <scroll-view class="flex-1" scroll-y scroll-with-animation :throttle="false">
        <view class="flex justify-between items-center flex-wrap">
          <view
            v-for="ite in areasList"
            :key="ite.value"
            :class="`flex items-center w-full h-[32px] !py-2 px-3 ${areas === ite.value ? ' !text-primary' : ''}`"
            @click="areas = ite.value"
          >
            {{ ite.label }}
          </view>
        </view>
      </scroll-view>
    </view>

    <!-- 底部按钮 -->
    <view class="fixed bottom-0 left-0 right-0 bg-white border-t border-gray-200 p-4 !pb-safe">
      <view class="pb-2 gap-1 flex-wrap flex">
        <tag
          v-for="item in selectedItems"
          :label="item.label"
          :value="item.value"
          :key="item.value"
          @on-close="handleSelect"
        ></tag>
      </view>
      <view class="flex gap-3">
        <wd-button type="info" custom-class="w-[33%]" :round="false" @click="resetSelection">
          取消
        </wd-button>
        <wd-button type="primary" custom-class="flex-1" :round="false" @click="confirmSelection">
          确定
        </wd-button>
      </view>
    </view>
  </view>
</template>

<script setup lang="ts">
import { ref, computed, onMounted } from 'vue'
import { locations } from '@/constant'
import { getLeafNodes, navigateBack } from '@/utils'
import { useCategoriesStore } from '@/store/categories'
const { setCategory } = useCategoriesStore()
// 响应式数据
const province = ref()
const cities = ref()
const areas = ref()
const categoriesRef = ref(locations)
const selectedCategories = ref<string[]>([])
const searchKeyword = ref('')
const citiesList = computed(() => {
  return locations.find((item) => item.value === province.value)?.children || []
})
const areasList = computed(() => {
  return citiesList.value.find((item) => item.value === cities.value)?.children || []
})

const selectedItems = computed(() => {
  let result = getLeafNodes(categoriesRef.value).filter((item) =>
    selectedCategories.value.includes(item.value),
  )
  return result
})

const handleSelect = (value) => {
  if (selectedCategories.value.includes(value)) {
    selectedCategories.value = selectedCategories.value.filter((item) => item !== value)
  } else {
    selectedCategories.value.push(value)
  }
}

const onConfirm = () => {
  const keyword = searchKeyword.value
  const result = locations.find((item) => item.label === keyword)
  if (result) {
    province.value = result.value
  } else {
    searchKeyword.value = ''
  }
}
// 重置选择
const resetSelection = () => {
  searchKeyword.value = ''
}

const confirmSelection = () => {
  setCategory(selectedItems.value)
  console.log(selectedItems.value)
  // navigateBack()
}
</script>
